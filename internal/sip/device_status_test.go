package sip

import (
	"testing"
	"time"

	"gb-gateway/internal/config"
	"gb-gateway/internal/state"
	"gb-gateway/pkg/models"
	"gb-gateway/pkg/utils"
)

func TestDeviceStatusHandling(t *testing.T) {
	// Create state manager
	stateManager := state.NewManager()

	// Create server config
	cfg := &config.ServerConfig{
		SIPID:   "34020000002000000001",
		SIPIP:   "*************",
		SIPPort: 5060,
	}

	// Create SIP server
	server := NewServer(cfg, stateManager)

	// Register a test platform
	platform := &models.Platform{
		ID:     "34020000002000000001",
		SIPURI: "sip:34020000002000000001@*************:5060",
		IP:     "*************",
		Port:   5060,
	}
	stateManager.RegisterPlatform(platform)

	// Add a test device
	device := models.Device{
		GBID:       "34020000001320000001",
		Name:       "Test Camera",
		Status:     "ON",
		PlatformID: platform.ID,
	}
	stateManager.UpdateDevices(platform.ID, []models.Device{device})

	// Test device status response XML
	statusXML := `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <CmdType>DeviceStatus</CmdType>
    <SN>123</SN>
    <DeviceID>34020000001320000001</DeviceID>
    <Result>OK</Result>
    <Online>ONLINE</Online>
    <Status>OK</Status>
    <DeviceTime>2024-01-01T12:00:00</DeviceTime>
</Response>`

	// Test handleDeviceStatus
	server.handleDeviceStatus(platform.ID, []byte(statusXML))

	// Verify device status was updated
	updatedDevice, exists := stateManager.GetDevice(device.GBID)
	if !exists {
		t.Fatal("Device not found after status update")
	}

	if updatedDevice.Online != "ONLINE" {
		t.Errorf("Expected Online=ONLINE, got %s", updatedDevice.Online)
	}

	if updatedDevice.Status != "OK" {
		t.Errorf("Expected Status=OK, got %s", updatedDevice.Status)
	}

	if updatedDevice.LastStatusUpdate == nil {
		t.Error("LastStatusUpdate should not be nil")
	}

	if updatedDevice.StatusExpiry == nil {
		t.Error("StatusExpiry should not be nil")
	}

	// Verify expiry time is set correctly (should be ~5 minutes from now)
	expectedExpiry := time.Now().Add(5 * time.Minute)
	if updatedDevice.StatusExpiry.Before(expectedExpiry.Add(-10*time.Second)) ||
		updatedDevice.StatusExpiry.After(expectedExpiry.Add(10*time.Second)) {
		t.Errorf("StatusExpiry not set correctly, got %v", updatedDevice.StatusExpiry)
	}
}

func TestDeviceStatusXMLParsing(t *testing.T) {
	statusXML := `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <CmdType>DeviceStatus</CmdType>
    <SN>456</SN>
    <DeviceID>34020000001320000002</DeviceID>
    <Result>OK</Result>
    <Online>OFFLINE</Online>
    <Status>ERROR</Status>
    <Reason>Network timeout</Reason>
    <DeviceTime>2024-01-01T12:00:00</DeviceTime>
</Response>`

	var deviceStatus models.DeviceStatusResponse
	err := utils.XMLDecode([]byte(statusXML), &deviceStatus)
	if err != nil {
		t.Fatalf("Failed to parse device status XML: %v", err)
	}

	if deviceStatus.CmdType != models.CmdType__DeviceStatus {
		t.Errorf("Expected CmdType=DeviceStatus, got %s", deviceStatus.CmdType)
	}

	if deviceStatus.SN != 456 {
		t.Errorf("Expected SN=456, got %d", deviceStatus.SN)
	}

	if deviceStatus.DeviceID != "34020000001320000002" {
		t.Errorf("Expected DeviceID=34020000001320000002, got %s", deviceStatus.DeviceID)
	}

	if deviceStatus.Online != "OFFLINE" {
		t.Errorf("Expected Online=OFFLINE, got %s", deviceStatus.Online)
	}

	if deviceStatus.Status != "ERROR" {
		t.Errorf("Expected Status=ERROR, got %s", deviceStatus.Status)
	}

	if deviceStatus.Reason != "Network timeout" {
		t.Errorf("Expected Reason='Network timeout', got %s", deviceStatus.Reason)
	}
}

func TestDeviceStatusExpiry(t *testing.T) {
	stateManager := state.NewManager()

	// Add a test device
	device := models.Device{
		GBID:       "34020000001320000001",
		Name:       "Test Camera",
		Status:     "ON",
		PlatformID: "34020000002000000001",
	}
	stateManager.UpdateDevices("34020000002000000001", []models.Device{device})

	// Update device status
	stateManager.UpdateDeviceStatus(device.GBID, "ONLINE", "OK")

	// Verify status was set
	updatedDevice, _ := stateManager.GetDevice(device.GBID)
	if updatedDevice.Online != "ONLINE" || updatedDevice.Status != "OK" {
		t.Error("Device status not updated correctly")
	}

	// Manually set expiry to past time to test cleanup
	pastTime := time.Now().Add(-1 * time.Minute)
	updatedDevice.StatusExpiry = &pastTime
	stateManager.UpdateDevices("34020000002000000001", []models.Device{*updatedDevice})

	// Wait a bit for cleanup to potentially run
	time.Sleep(100 * time.Millisecond)

	// Note: In a real test, we'd need to trigger cleanup manually
	// since the cleanup goroutine runs every minute
}
