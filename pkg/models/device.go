package models

import "time"

// Device represents a camera device
type Device struct {
	GBID             string     `json:"gb_id" xml:"DeviceID"`
	Name             string     `json:"name" xml:"Name"`
	Status           string     `json:"status" xml:"Status"`
	IP               string     `json:"ip,omitempty" xml:"IPAddress,omitempty"`
	PlatformID       string     `json:"platform_id"`
	Online           string     `json:"online,omitempty"`
	LastStatusUpdate *time.Time `json:"last_status_update,omitempty"`
	StatusExpiry     *time.Time `json:"status_expiry,omitempty"`
}

// DeviceList represents the response from catalog query
type DeviceList struct {
	Devices []Device `json:"devices"`
	Total   int      `json:"total"`
}
